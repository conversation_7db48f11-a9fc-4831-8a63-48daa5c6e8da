allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    afterEvaluate { project ->
        if (project.hasProperty("android")) {
            android {
                compileSdkVersion 35

                if (project.hasProperty("buildTypes")) {
                    buildTypes {
                        release {
                            minifyEnabled false
                            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
                        }
                    }
                }

                compileOptions {
                    sourceCompatibility JavaVersion.VERSION_11
                    targetCompatibility JavaVersion.VERSION_11
                }
            }
        }

        // Configure JVM toolchain for all projects
        if (project.hasProperty("kotlin")) {
            project.kotlin {
                jvmToolchain(11)
            }
        }
    }
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
