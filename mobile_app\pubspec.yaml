name: trustchain_auth
description: "Trust<PERSON><PERSON><PERSON>-Auth: Behavioral Biometrics Banking App"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI and Design
  cupertino_icons: ^1.0.8
  material_design_icons_flutter: ^7.0.7296
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9

  # State Management
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5

  # Navigation
  go_router: ^12.1.3

  # Local Storage & Security
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  flutter_secure_storage: ^9.0.0
  crypto: ^3.0.3

  # Sensors & Device Info
  sensors_plus: ^4.0.2
  device_info_plus: ^9.1.1
  permission_handler: ^11.1.0
  geolocator: ^10.1.0
  battery_plus: ^5.0.2

  # ML and TensorFlow Lite
  tflite_flutter: ^0.11.0

  # HTTP and API
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1

  # Utilities
  intl: ^0.19.0
  uuid: ^4.2.1
  path_provider: ^2.1.1
  shared_preferences: ^2.2.2

  # Logging and Analytics
  logger: ^2.0.2+1

  # Charts and Visualization
  fl_chart: ^0.65.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Linting and Code Quality
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.7
  hive_generator: ^2.0.1
  retrofit_generator: ^8.0.6
  json_serializable: ^6.7.1

  # Testing
  mockito: ^5.4.4
  bloc_test: ^9.1.5
  integration_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets for TrustChain-Auth Banking App
  assets:
    - assets/images/
    - assets/icons/
    - assets/ml_models/
    - assets/animations/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
